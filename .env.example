# 微信支付配置
# 获取方式：https://pay.weixin.qq.com/
WECHAT_MCHID=1900000109
WECHAT_APPID=wxd678efh567hg6787
WECHAT_CERT_SERIAL_NO=444F4864EA9B34415...
WECHAT_APIV3_KEY=MIIEvwIBADANBgkqhkiG9w0BAQE...
WECHAT_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----
MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQC...
-----END PRIVATE KEY-----
WECHAT_NOTIFY_URL=https://your-domain.com/api/wechat/notify

# 支付宝配置
# 获取方式：https://open.alipay.com/
ALIPAY_APPID=2021000000000000
ALIPAY_PRIVATE_KEY=-----BEGIN RSA PRIVATE KEY-----
MIIEpAIBAAKCAQEA...
-----END RSA PRIVATE KEY-----
ALIPAY_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
-----END PUBLIC KEY-----
ALIPAY_NOTIFY_URL=https://your-domain.com/api/alipay/notify
ALIPAY_DEBUG=True

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=5000
SERVER_DEBUG=True

# 安全配置
API_KEY=your-secure-api-key-here
JWT_SECRET=your-jwt-secret-here

# 数据库配置（可选，用于生产环境）
DATABASE_URL=sqlite:///payments.db
# DATABASE_URL=postgresql://user:password@localhost/payments
# DATABASE_URL=mysql://user:password@localhost/payments
