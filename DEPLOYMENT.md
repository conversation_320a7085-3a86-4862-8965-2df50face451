# 🚀 真实支付插件部署指南

本指南将帮助您部署一个可以真实支付的京东支付二维码助手。

## 📋 前置要求

### 1. 支付平台账号申请

#### 微信支付
1. **申请微信商户号**
   - 访问：https://pay.weixin.qq.com/
   - 提交企业资质和相关证件
   - 等待审核通过（通常3-7个工作日）

2. **获取必要信息**
   - 商户号 (MCHID)
   - 应用ID (APPID)
   - 商户证书序列号
   - APIv3密钥
   - 商户私钥文件

#### 支付宝
1. **申请支付宝开放平台账号**
   - 访问：https://open.alipay.com/
   - 创建应用并提交审核
   - 配置应用公钥和获取支付宝公钥

2. **获取必要信息**
   - 应用ID (APPID)
   - 应用私钥
   - 支付宝公钥

### 2. 服务器要求

- **操作系统**: Linux (推荐 Ubuntu 20.04+)
- **Python**: 3.8+
- **内存**: 最少 1GB
- **存储**: 最少 10GB
- **网络**: 公网IP，支持HTTPS

## 🛠️ 部署步骤

### 步骤1: 服务器准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install python3 python3-pip nginx certbot python3-certbot-nginx git -y

# 安装Python依赖管理工具
pip3 install pipenv
```

### 步骤2: 下载项目代码

```bash
# 克隆项目
git clone https://github.com/your-username/jd-payment-qr-helper.git
cd jd-payment-qr-helper

# 或者直接上传文件到服务器
```

### 步骤3: 安装Python依赖

```bash
# 安装依赖
pip3 install -r requirements.txt

# 主要依赖包括：
# flask
# flask-cors
# wechatpayv3
# python-alipay-sdk
# qrcode
# python-dotenv
```

创建 `requirements.txt` 文件：

```txt
Flask==2.3.3
Flask-CORS==4.0.0
wechatpayv3==1.2.5
python-alipay-sdk==3.0.4
qrcode==7.4.2
python-dotenv==1.0.0
gunicorn==21.2.0
```

### 步骤4: 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
nano .env
```

填入您的真实支付平台信息：

```bash
# 微信支付配置
WECHAT_MCHID=您的微信商户号
WECHAT_APPID=您的微信应用ID
WECHAT_CERT_SERIAL_NO=您的证书序列号
WECHAT_APIV3_KEY=您的APIv3密钥
WECHAT_PRIVATE_KEY=您的商户私钥内容
WECHAT_NOTIFY_URL=https://您的域名.com/api/wechat/notify

# 支付宝配置
ALIPAY_APPID=您的支付宝应用ID
ALIPAY_PRIVATE_KEY=您的应用私钥
ALIPAY_PUBLIC_KEY=支付宝公钥
ALIPAY_NOTIFY_URL=https://您的域名.com/api/alipay/notify
ALIPAY_DEBUG=False
```

### 步骤5: 配置HTTPS证书

```bash
# 使用Let's Encrypt获取免费SSL证书
sudo certbot --nginx -d 您的域名.com

# 配置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 步骤6: 配置Nginx

创建Nginx配置文件：

```bash
sudo nano /etc/nginx/sites-available/payment-server
```

添加以下配置：

```nginx
server {
    listen 80;
    server_name 您的域名.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name 您的域名.com;

    ssl_certificate /etc/letsencrypt/live/您的域名.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/您的域名.com/privkey.pem;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

启用配置：

```bash
sudo ln -s /etc/nginx/sites-available/payment-server /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 步骤7: 创建系统服务

创建systemd服务文件：

```bash
sudo nano /etc/systemd/system/payment-server.service
```

添加以下内容：

```ini
[Unit]
Description=Payment Server
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/jd-payment-qr-helper
Environment=PATH=/usr/bin:/usr/local/bin
ExecStart=/usr/local/bin/gunicorn --bind 127.0.0.1:5000 --workers 4 payment-server:app
Restart=always

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable payment-server
sudo systemctl start payment-server
sudo systemctl status payment-server
```

### 步骤8: 更新浏览器插件配置

修改 `content.js` 中的服务器地址：

```javascript
// 将以下URL替换为您的实际服务器地址
const response = await fetch('https://您的域名.com/api/create-payment', {
    // ...
});
```

## 🔒 安全配置

### 1. 防火墙设置

```bash
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
```

### 2. API密钥认证

在 `.env` 文件中设置API密钥：

```bash
API_KEY=your-very-secure-api-key-here
```

更新服务器代码以验证API密钥。

### 3. 限制访问

配置Nginx限制访问频率：

```nginx
http {
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    
    server {
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            # ... 其他配置
        }
    }
}
```

## 📊 监控和日志

### 1. 日志配置

```bash
# 创建日志目录
sudo mkdir -p /var/log/payment-server
sudo chown www-data:www-data /var/log/payment-server

# 配置日志轮转
sudo nano /etc/logrotate.d/payment-server
```

添加日志轮转配置：

```
/var/log/payment-server/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

### 2. 监控脚本

创建健康检查脚本：

```bash
#!/bin/bash
# health-check.sh

URL="https://您的域名.com/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $URL)

if [ $RESPONSE -eq 200 ]; then
    echo "$(date): Service is healthy"
else
    echo "$(date): Service is down (HTTP $RESPONSE)"
    # 发送告警通知
    # systemctl restart payment-server
fi
```

设置定时检查：

```bash
# 添加到crontab
*/5 * * * * /path/to/health-check.sh >> /var/log/health-check.log
```

## 🧪 测试

### 1. 服务器测试

```bash
# 测试健康检查
curl https://您的域名.com/health

# 测试创建支付订单
curl -X POST https://您的域名.com/api/create-payment \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "paymentMethod": "wechat",
    "amount": 100,
    "subject": "测试商品",
    "orderId": "TEST123456"
  }'
```

### 2. 插件测试

1. 在Chrome中加载插件
2. 访问京东支付页面
3. 点击"跳转网银支付"
4. 验证是否显示真实的支付二维码

## 🚨 故障排除

### 常见问题

1. **支付回调失败**
   - 检查回调URL是否可访问
   - 验证SSL证书是否有效
   - 查看服务器日志

2. **二维码生成失败**
   - 检查支付平台配置
   - 验证API密钥是否正确
   - 查看错误日志

3. **服务器无响应**
   - 检查服务状态：`sudo systemctl status payment-server`
   - 查看日志：`sudo journalctl -u payment-server -f`
   - 重启服务：`sudo systemctl restart payment-server`

### 日志查看

```bash
# 查看应用日志
sudo journalctl -u payment-server -f

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 查看系统日志
sudo tail -f /var/log/syslog
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 检查所有配置文件是否正确
2. 确认支付平台账号状态正常
3. 查看详细的错误日志
4. 联系技术支持团队

---

**注意**: 本系统涉及真实资金交易，请确保在生产环境中采取适当的安全措施，并遵守相关法律法规。
