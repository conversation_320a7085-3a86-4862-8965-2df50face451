# 🚀 京东支付二维码助手 - 安装指南

## 📦 插件安装步骤

### 1. 准备文件
确保您的文件夹包含以下文件：
```
京东支付二维码助手/
├── manifest.json          ✅ 插件清单文件
├── content.js             ✅ 内容脚本
├── background.js          ✅ 后台脚本
├── popup.html            ✅ 弹出页面
├── popup.js              ✅ 弹出页面脚本
├── styles.css            ✅ 样式文件
├── icons/
│   ├── icon16.png        ✅ 16x16图标
│   ├── icon32.png        ✅ 32x32图标
│   ├── icon48.png        ✅ 48x48图标
│   └── icon128.png       ✅ 128x128图标
└── libs/
    └── qrcode.min.js     ✅ QR码生成库
```

### 2. 在Chrome中安装插件

#### 步骤 2.1: 打开Chrome扩展管理页面
- 在Chrome浏览器地址栏输入：`chrome://extensions/`
- 或者：点击菜单 → 更多工具 → 扩展程序

#### 步骤 2.2: 启用开发者模式
- 在扩展程序页面右上角，打开"开发者模式"开关

#### 步骤 2.3: 加载插件
- 点击"加载已解压的扩展程序"按钮
- 选择包含所有插件文件的文件夹（`D:\vs\23`）
- 点击"选择文件夹"

#### 步骤 2.4: 验证安装
- 插件应该出现在扩展程序列表中
- 浏览器工具栏应该显示插件图标 🏦
- 状态应该显示为"已启用"

### 3. 配置插件

#### 步骤 3.1: 点击插件图标
- 在浏览器工具栏点击插件图标
- 弹出设置页面

#### 步骤 3.2: 配置默认设置
- **启用插件**: 确保开关处于开启状态
- **默认支付方式**: 选择您偏好的支付方式（微信支付/支付宝）
- **自动检测**: 建议开启
- **显示通知**: 建议开启

### 4. 测试插件

#### 步骤 4.1: 访问京东支付页面
- 在京东购买商品，进入支付页面
- URL应该类似：`https://payc.m.jd.com/d/cashier/...`

#### 步骤 4.2: 测试拦截功能
- 点击"添加新卡 / 网银支付"
- 选择"网银支付"标签
- 选择任意银行
- 点击"跳转网银并支付"

#### 步骤 4.3: 验证结果
- 应该弹出二维码窗口而不是跳转到银行网站
- 窗口显示订单信息和支付二维码
- 可以选择不同的支付方式

## ⚠️ 常见问题解决

### 问题1: "无法加载清单"
**原因**: 缺少必需文件或文件路径错误
**解决**: 
- 确保所有文件都在正确位置
- 检查manifest.json语法是否正确
- 确保图标文件存在

### 问题2: 插件图标不显示
**原因**: 图标文件缺失或格式错误
**解决**: 
- 运行 `python generate_icons.py` 重新生成图标
- 确保icons文件夹包含所有尺寸的PNG文件

### 问题3: 插件不工作
**原因**: 权限不足或脚本错误
**解决**: 
- 检查浏览器控制台是否有错误信息
- 确保在京东支付页面测试
- 重新加载插件

### 问题4: 二维码不显示
**原因**: QR码库加载失败
**解决**: 
- 确保 `libs/qrcode.min.js` 文件存在
- 检查网络连接
- 查看浏览器控制台错误

## 🔧 高级配置

### 启用真实支付功能
如果您想使用真实支付功能，需要：

1. **部署支付服务器**
   ```bash
   pip install -r requirements.txt
   python payment-server.py
   ```

2. **配置支付平台**
   - 申请微信支付商户号
   - 申请支付宝开放平台账号
   - 配置环境变量

3. **更新插件配置**
   - 修改 `content.js` 中的服务器地址
   - 重新加载插件

详细配置请参考：[DEPLOYMENT.md](DEPLOYMENT.md)

## 📞 技术支持

如果遇到问题：
1. 检查浏览器控制台错误信息
2. 确认所有文件完整
3. 尝试重新安装插件
4. 查看详细文档

---

**提示**: 首次安装建议先在测试环境验证功能，确认无误后再在生产环境使用。
