# 🏦 京东支付二维码助手

一个强大的浏览器插件，可以拦截京东支付页面的网银跳转，直接显示银行支付二维码，让您通过手机银行APP扫码完成支付，无需登录网银。

## ✨ 功能特性

- 🚫 **拦截网银跳转** - 自动拦截"跳转网银支付"操作
- 📱 **生成支付二维码** - 直接显示银行支付二维码
- 🏦 **支持多家银行** - 工商银行、建设银行、农业银行、中国银行等
- ⚙️ **智能识别** - 自动提取订单信息和支付金额
- 🎨 **美观界面** - 现代化的用户界面设计
- 📊 **使用统计** - 记录拦截次数和使用情况

## 🚀 安装方法

### 方法一：开发者模式安装（推荐）

1. **下载插件文件**
   - 下载所有插件文件到本地文件夹

2. **打开Chrome扩展管理页面**
   - 在Chrome浏览器中输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

4. **加载插件**
   - 点击"加载已解压的扩展程序"
   - 选择包含插件文件的文件夹

5. **完成安装**
   - 插件图标将出现在浏览器工具栏中

### 方法二：打包安装

1. 将所有文件打包为 `.zip` 文件
2. 在扩展管理页面点击"加载已解压的扩展程序"
3. 选择解压后的文件夹

## 📋 使用说明

### 基本使用

1. **访问京东支付页面**
   - 在京东购物并进入支付页面

2. **选择网银支付**
   - 点击"添加新卡 / 网银支付"
   - 选择"网银支付"标签

3. **选择银行**
   - 选择您要使用的银行

4. **点击跳转网银支付**
   - 插件会自动拦截跳转
   - 显示支付二维码弹窗

5. **扫码支付**
   - 使用手机银行APP扫描二维码
   - 完成支付

### 插件设置

点击浏览器工具栏中的插件图标，可以进行以下设置：

- **启用/禁用插件** - 控制插件是否工作
- **选择默认银行** - 设置首选银行
- **自动检测** - 自动识别支付页面
- **显示通知** - 拦截成功时显示通知

## 🏦 支持的银行

- 🏦 中国工商银行 (ICBC)
- 🏛️ 中国建设银行 (CCB)
- 🌾 中国农业银行 (ABC)
- 🏪 中国银行 (BOC)
- 🚇 交通银行 (COMM)
- 💳 招商银行 (CMB)
- 📮 中国邮政储蓄银行 (PSBC)
- 🏢 中信银行 (CITIC)

## 🔧 技术实现

### 核心技术

- **Manifest V3** - 使用最新的Chrome扩展API
- **内容脚本注入** - 监控和拦截页面操作
- **QR码生成** - 使用qrcode.js库生成二维码
- **现代化UI** - 响应式设计，支持暗色主题

### 文件结构

```
京东支付二维码助手/
├── manifest.json          # 插件清单文件
├── content.js             # 内容脚本
├── background.js          # 后台脚本
├── popup.html            # 弹出页面
├── popup.js              # 弹出页面脚本
├── styles.css            # 样式文件
├── libs/
│   └── qrcode.min.js     # QR码生成库
├── icons/
│   ├── icon16.png        # 16x16图标
│   ├── icon32.png        # 32x32图标
│   ├── icon48.png        # 48x48图标
│   └── icon128.png       # 128x128图标
└── README.md             # 说明文档
```

## 🛡️ 安全说明

- 插件仅在京东支付页面工作
- 不收集任何个人信息
- 不存储支付数据
- 所有操作在本地完成

## ❓ 常见问题

### Q: 插件不工作怎么办？
A: 请检查：
- 是否在京东支付页面
- 插件是否已启用
- 浏览器是否支持Manifest V3

### Q: 二维码无法扫描？
A: 请尝试：
- 刷新二维码
- 检查网络连接
- 确认银行APP支持扫码支付

### Q: 支持其他购物网站吗？
A: 目前仅支持京东，后续版本可能会添加其他网站支持

## 🔄 更新日志

### v1.0.0 (2024-08-02)
- 🎉 首次发布
- ✅ 支持京东支付页面拦截
- ✅ 支持8家主要银行
- ✅ 现代化UI设计
- ✅ 完整的设置功能

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 检查浏览器控制台是否有错误信息
2. 确认插件版本是否为最新
3. 尝试重新安装插件

## 📄 许可证

本项目采用 MIT 许可证，详情请参阅 LICENSE 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

---

**注意：本插件仅用于技术演示和学习目的，请确保在使用时遵守相关法律法规和网站服务条款。**
