/**
 * 京东支付二维码助手 - 后台脚本
 * 处理插件的后台逻辑和设置管理
 */

class JDPaymentQRBackground {
    constructor() {
        this.init();
    }

    init() {
        console.log('京东支付二维码助手后台服务已启动');
        
        // 监听插件安装
        chrome.runtime.onInstalled.addListener(this.handleInstalled.bind(this));
        
        // 监听来自内容脚本的消息
        chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));
        
        // 监听标签页更新
        chrome.tabs.onUpdated.addListener(this.handleTabUpdated.bind(this));
        
        // 初始化默认设置
        this.initializeSettings();
    }

    handleInstalled(details) {
        if (details.reason === 'install') {
            console.log('插件首次安装');
            this.showWelcomeNotification();
            this.setDefaultSettings();
        } else if (details.reason === 'update') {
            console.log('插件已更新');
            this.handleUpdate(details.previousVersion);
        }
    }

    async initializeSettings() {
        try {
            const settings = await chrome.storage.sync.get([
                'enabled',
                'preferredBank',
                'autoDetect',
                'showNotifications'
            ]);

            // 设置默认值
            const defaultSettings = {
                enabled: true,
                preferredBank: 'ICBC',
                autoDetect: true,
                showNotifications: true,
                ...settings
            };

            await chrome.storage.sync.set(defaultSettings);
            console.log('设置已初始化:', defaultSettings);
        } catch (error) {
            console.error('初始化设置失败:', error);
        }
    }

    setDefaultSettings() {
        const defaultSettings = {
            enabled: true,
            preferredBank: 'ICBC',
            autoDetect: true,
            showNotifications: true,
            interceptCount: 0,
            lastUsed: Date.now()
        };

        chrome.storage.sync.set(defaultSettings);
    }

    handleMessage(message, sender, sendResponse) {
        console.log('收到消息:', message);

        switch (message.type) {
            case 'PAYMENT_INTERCEPTED':
                this.handlePaymentIntercepted(message.data);
                break;
                
            case 'GENERATE_QR':
                this.generateQRCode(message.data)
                    .then(result => sendResponse({ success: true, data: result }))
                    .catch(error => sendResponse({ success: false, error: error.message }));
                return true; // 保持消息通道开放
                
            case 'GET_SETTINGS':
                this.getSettings()
                    .then(settings => sendResponse({ success: true, data: settings }))
                    .catch(error => sendResponse({ success: false, error: error.message }));
                return true;
                
            case 'UPDATE_SETTINGS':
                this.updateSettings(message.data)
                    .then(() => sendResponse({ success: true }))
                    .catch(error => sendResponse({ success: false, error: error.message }));
                return true;
                
            case 'GET_STATISTICS':
                this.getStatistics()
                    .then(stats => sendResponse({ success: true, data: stats }))
                    .catch(error => sendResponse({ success: false, error: error.message }));
                return true;
        }
    }

    async handlePaymentIntercepted(data) {
        try {
            // 更新统计信息
            const stats = await chrome.storage.local.get(['interceptCount', 'lastIntercepted']);
            const newCount = (stats.interceptCount || 0) + 1;
            
            await chrome.storage.local.set({
                interceptCount: newCount,
                lastIntercepted: Date.now(),
                lastPaymentData: data
            });

            // 显示通知（如果启用）
            const settings = await chrome.storage.sync.get(['showNotifications']);
            if (settings.showNotifications) {
                this.showInterceptNotification(data);
            }

            console.log(`支付拦截成功，总计: ${newCount} 次`);
        } catch (error) {
            console.error('处理支付拦截失败:', error);
        }
    }

    showInterceptNotification(data) {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: '京东支付二维码助手',
            message: `已拦截网银支付跳转\n订单: ${data.orderId}\n金额: ¥${data.amount}`
        });
    }

    showWelcomeNotification() {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: '京东支付二维码助手',
            message: '插件安装成功！现在可以在京东支付页面直接显示银行支付二维码了。'
        });
    }

    handleTabUpdated(tabId, changeInfo, tab) {
        // 检查是否是京东支付页面
        if (changeInfo.status === 'complete' && tab.url) {
            const isJDPaymentPage = /payc\.m\.jd\.com|pay\.jd\.com|cashier\.jd\.com/.test(tab.url);
            
            if (isJDPaymentPage) {
                console.log('检测到京东支付页面:', tab.url);
                this.updateBadge(tabId, true);
            } else {
                this.updateBadge(tabId, false);
            }
        }
    }

    updateBadge(tabId, isActive) {
        if (isActive) {
            chrome.action.setBadgeText({ text: 'ON', tabId });
            chrome.action.setBadgeBackgroundColor({ color: '#4CAF50', tabId });
            chrome.action.setTitle({ 
                title: '京东支付二维码助手 - 已激活', 
                tabId 
            });
        } else {
            chrome.action.setBadgeText({ text: '', tabId });
            chrome.action.setTitle({ 
                title: '京东支付二维码助手', 
                tabId 
            });
        }
    }

    async generateQRCode(paymentData) {
        try {
            // 这里可以调用外部API生成二维码
            // 或者返回支付URL让前端生成二维码
            
            const settings = await chrome.storage.sync.get(['preferredBank']);
            const bankCode = settings.preferredBank || 'ICBC';
            
            // 构建支付URL
            const paymentUrl = this.buildPaymentUrl(bankCode, paymentData);
            
            return {
                paymentUrl,
                bankCode,
                timestamp: Date.now()
            };
        } catch (error) {
            console.error('生成二维码失败:', error);
            throw error;
        }
    }

    buildPaymentUrl(bankCode, paymentData) {
        const bankConfigs = {
            'ICBC': 'https://b2c.icbc.com.cn/servlet/ICBCEPCCEBizServlet',
            'CCB': 'https://ibsbjstar.ccb.com.cn/CCBIS/B2CMainPlat_00_BEFORE',
            'ABC': 'https://pay3.95599.cn/pay/OnLinePay',
            'BOC': 'https://ebsnew.boc.cn/boc15/welcome.html'
        };

        const baseUrl = bankConfigs[bankCode] || bankConfigs['ICBC'];
        
        const params = new URLSearchParams({
            orderId: paymentData.orderId,
            amount: paymentData.amount,
            subject: paymentData.subject,
            bankCode: bankCode,
            timestamp: Date.now(),
            source: 'jd_qr_helper'
        });

        return `${baseUrl}?${params.toString()}`;
    }

    async getSettings() {
        try {
            const settings = await chrome.storage.sync.get([
                'enabled',
                'preferredBank',
                'autoDetect',
                'showNotifications'
            ]);
            return settings;
        } catch (error) {
            console.error('获取设置失败:', error);
            throw error;
        }
    }

    async updateSettings(newSettings) {
        try {
            await chrome.storage.sync.set(newSettings);
            console.log('设置已更新:', newSettings);
        } catch (error) {
            console.error('更新设置失败:', error);
            throw error;
        }
    }

    async getStatistics() {
        try {
            const stats = await chrome.storage.local.get([
                'interceptCount',
                'lastIntercepted',
                'lastPaymentData'
            ]);
            
            return {
                interceptCount: stats.interceptCount || 0,
                lastIntercepted: stats.lastIntercepted || null,
                lastPaymentData: stats.lastPaymentData || null
            };
        } catch (error) {
            console.error('获取统计信息失败:', error);
            throw error;
        }
    }

    handleUpdate(previousVersion) {
        console.log(`插件从版本 ${previousVersion} 更新到当前版本`);
        
        // 这里可以处理版本更新逻辑
        // 比如迁移旧设置、清理过期数据等
    }
}

// 初始化后台服务
new JDPaymentQRBackground();
