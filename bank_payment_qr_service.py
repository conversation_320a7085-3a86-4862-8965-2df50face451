#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
银行支付二维码生成服务
支持工商银行、建设银行、农业银行、中国银行等主要银行的支付二维码生成
"""

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import qrcode
import io
import base64
import json
import hashlib
import time
import uuid
from datetime import datetime
import logging

app = Flask(__name__)
CORS(app)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 银行配置信息
BANK_CONFIGS = {
    'ICBC': {  # 工商银行
        'name': '中国工商银行',
        'code': 'ICBC',
        'gateway_url': 'https://b2c.icbc.com.cn/servlet/ICBCEPCCEBizServlet',
        'qr_prefix': 'icbc://pay/',
        'merchant_id': 'your_icbc_merchant_id',
        'app_key': 'your_icbc_app_key'
    },
    'CCB': {   # 建设银行
        'name': '中国建设银行',
        'code': 'CCB',
        'gateway_url': 'https://ibsbjstar.ccb.com.cn/CCBIS/B2CMainPlat_00_BEFORE',
        'qr_prefix': 'ccb://pay/',
        'merchant_id': 'your_ccb_merchant_id',
        'app_key': 'your_ccb_app_key'
    },
    'ABC': {   # 农业银行
        'name': '中国农业银行',
        'code': 'ABC',
        'gateway_url': 'https://pay3.95599.cn/pay/OnLinePay',
        'qr_prefix': 'abc://pay/',
        'merchant_id': 'your_abc_merchant_id',
        'app_key': 'your_abc_app_key'
    },
    'BOC': {   # 中国银行
        'name': '中国银行',
        'code': 'BOC',
        'gateway_url': 'https://ebsnew.boc.cn/boc15/welcome.html',
        'qr_prefix': 'boc://pay/',
        'merchant_id': 'your_boc_merchant_id',
        'app_key': 'your_boc_app_key'
    }
}

def generate_order_id():
    """生成唯一订单号"""
    timestamp = str(int(time.time()))
    random_str = str(uuid.uuid4()).replace('-', '')[:8]
    return f"PAY{timestamp}{random_str}"

def generate_signature(params, app_key):
    """生成签名"""
    # 按键名排序
    sorted_params = sorted(params.items())
    # 拼接参数
    param_str = '&'.join([f"{k}={v}" for k, v in sorted_params])
    # 添加密钥
    sign_str = f"{param_str}&key={app_key}"
    # MD5加密
    return hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()

def create_payment_url(bank_code, order_data):
    """创建银行支付URL"""
    bank_config = BANK_CONFIGS.get(bank_code)
    if not bank_config:
        raise ValueError(f"不支持的银行代码: {bank_code}")
    
    # 构建支付参数
    payment_params = {
        'merchant_id': bank_config['merchant_id'],
        'order_id': order_data['order_id'],
        'amount': order_data['amount'],
        'currency': 'CNY',
        'subject': order_data['subject'],
        'body': order_data.get('body', ''),
        'notify_url': order_data.get('notify_url', ''),
        'return_url': order_data.get('return_url', ''),
        'timestamp': str(int(time.time())),
        'version': '1.0',
        'sign_type': 'MD5'
    }
    
    # 生成签名
    signature = generate_signature(payment_params, bank_config['app_key'])
    payment_params['sign'] = signature
    
    # 构建支付URL
    param_str = '&'.join([f"{k}={v}" for k, v in payment_params.items()])
    payment_url = f"{bank_config['qr_prefix']}?{param_str}"
    
    return payment_url

def generate_qr_code(data, size=10, border=4):
    """生成二维码"""
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=size,
        border=border,
    )
    qr.add_data(data)
    qr.make(fit=True)
    
    # 创建二维码图片
    img = qr.make_image(fill_color="black", back_color="white")
    
    # 转换为字节流
    img_buffer = io.BytesIO()
    img.save(img_buffer, format='PNG')
    img_buffer.seek(0)
    
    return img_buffer

@app.route('/api/bank/payment/qr', methods=['POST'])
def create_bank_payment_qr():
    """创建银行支付二维码"""
    try:
        data = request.get_json()
        
        # 验证必需参数
        required_fields = ['bank_code', 'amount', 'subject', 'order_id']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'缺少必需参数: {field}'
                }), 400
        
        bank_code = data['bank_code'].upper()
        
        # 验证银行代码
        if bank_code not in BANK_CONFIGS:
            return jsonify({
                'success': False,
                'error': f'不支持的银行: {bank_code}',
                'supported_banks': list(BANK_CONFIGS.keys())
            }), 400
        
        # 构建订单数据
        order_data = {
            'order_id': data['order_id'],
            'amount': data['amount'],
            'subject': data['subject'],
            'body': data.get('body', ''),
            'notify_url': data.get('notify_url', ''),
            'return_url': data.get('return_url', '')
        }
        
        # 创建支付URL
        payment_url = create_payment_url(bank_code, order_data)
        
        # 生成二维码
        qr_buffer = generate_qr_code(payment_url)
        
        # 转换为base64
        qr_base64 = base64.b64encode(qr_buffer.getvalue()).decode('utf-8')
        
        # 记录日志
        logger.info(f"生成银行支付二维码 - 银行: {bank_code}, 订单: {order_data['order_id']}, 金额: {order_data['amount']}")
        
        return jsonify({
            'success': True,
            'data': {
                'bank_name': BANK_CONFIGS[bank_code]['name'],
                'bank_code': bank_code,
                'order_id': order_data['order_id'],
                'amount': order_data['amount'],
                'subject': order_data['subject'],
                'payment_url': payment_url,
                'qr_code_base64': qr_base64,
                'qr_code_url': f"/api/bank/payment/qr/image/{order_data['order_id']}",
                'created_at': datetime.now().isoformat()
            }
        })
        
    except Exception as e:
        logger.error(f"生成银行支付二维码失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/bank/payment/qr/image/<order_id>')
def get_qr_image(order_id):
    """获取二维码图片"""
    try:
        # 这里应该从数据库或缓存中获取订单信息
        # 为演示目的，我们重新生成二维码
        
        # 模拟订单数据（实际应用中应从数据库获取）
        order_data = {
            'order_id': order_id,
            'amount': '35.61',
            'subject': 'TMT护腕腱鞘炎防扭伤运动TFCC护腕',
            'body': '商品购买'
        }
        
        # 默认使用工商银行
        payment_url = create_payment_url('ICBC', order_data)
        qr_buffer = generate_qr_code(payment_url)
        
        return send_file(
            qr_buffer,
            mimetype='image/png',
            as_attachment=False,
            download_name=f'qr_{order_id}.png'
        )
        
    except Exception as e:
        logger.error(f"获取二维码图片失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/banks', methods=['GET'])
def get_supported_banks():
    """获取支持的银行列表"""
    banks = []
    for code, config in BANK_CONFIGS.items():
        banks.append({
            'code': code,
            'name': config['name'],
            'logo_url': f'/static/images/banks/{code.lower()}_logo.png'
        })
    
    return jsonify({
        'success': True,
        'data': banks
    })

@app.route('/api/bank/payment/status/<order_id>')
def check_payment_status(order_id):
    """检查支付状态"""
    try:
        # 这里应该调用银行API查询支付状态
        # 为演示目的，返回模拟状态
        
        return jsonify({
            'success': True,
            'data': {
                'order_id': order_id,
                'status': 'pending',  # pending, success, failed, cancelled
                'message': '等待支付',
                'updated_at': datetime.now().isoformat()
            }
        })
        
    except Exception as e:
        logger.error(f"查询支付状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
