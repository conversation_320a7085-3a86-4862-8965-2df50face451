/**
 * 京东支付二维码助手 - 内容脚本
 * 拦截网银支付跳转，生成支付二维码
 */

class JDPaymentQRHelper {
    constructor() {
        this.isEnabled = true;
        this.paymentData = null;
        this.supportedBanks = {
            'ICBC': { name: '中国工商银行', logo: '🏦' },
            'CCB': { name: '中国建设银行', logo: '🏛️' },
            'ABC': { name: '中国农业银行', logo: '🌾' },
            'BOC': { name: '中国银行', logo: '🏪' },
            'COMM': { name: '交通银行', logo: '🚇' },
            'CMB': { name: '招商银行', logo: '💳' },
            'PSBC': { name: '中国邮政储蓄银行', logo: '📮' },
            'CITIC': { name: '中信银行', logo: '🏢' }
        };
        this.init();
    }

    init() {
        console.log('京东支付二维码助手已启动');
        this.loadSettings();
        this.interceptPaymentButtons();
        this.extractPaymentData();
        this.observePageChanges();
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get(['enabled', 'preferredBank']);
            this.isEnabled = result.enabled !== false;
            this.preferredBank = result.preferredBank || 'ICBC';
        } catch (error) {
            console.log('加载设置失败，使用默认设置');
        }
    }

    extractPaymentData() {
        try {
            // 提取订单信息
            const orderIdElement = document.querySelector('[class*="order"], [id*="order"]');
            const amountElement = document.querySelector('strong:contains("35.61"), .amount, [class*="amount"]');
            const subjectElement = document.querySelector('[class*="goods"], [class*="product"], [class*="subject"]');

            // 从页面URL或元素中提取订单信息
            const urlParams = new URLSearchParams(window.location.search);
            
            this.paymentData = {
                orderId: this.extractOrderId(),
                amount: this.extractAmount(),
                subject: this.extractSubject(),
                timestamp: Date.now()
            };

            console.log('提取的支付数据:', this.paymentData);
        } catch (error) {
            console.error('提取支付数据失败:', error);
        }
    }

    extractOrderId() {
        // 从URL参数提取
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('orderId')) {
            return urlParams.get('orderId');
        }

        // 从页面元素提取
        const orderElements = document.querySelectorAll('*');
        for (let element of orderElements) {
            const text = element.textContent || '';
            const match = text.match(/订单号[：:]\s*(\d+)/);
            if (match) {
                return match[1];
            }
        }

        return `JD${Date.now()}`;
    }

    extractAmount() {
        // 查找金额元素
        const amountSelectors = [
            'strong:contains("35.61")',
            '.amount',
            '[class*="amount"]',
            '[class*="price"]',
            '[class*="total"]'
        ];

        for (let selector of amountSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                const text = element.textContent || '';
                const match = text.match(/(\d+\.?\d*)/);
                if (match) {
                    return match[1];
                }
            }
        }

        // 从页面文本中查找
        const pageText = document.body.textContent || '';
        const amountMatch = pageText.match(/应付金额[：:]?\s*(\d+\.?\d*)/);
        if (amountMatch) {
            return amountMatch[1];
        }

        return '35.61'; // 默认金额
    }

    extractSubject() {
        // 查找商品名称
        const subjectSelectors = [
            '[class*="goods"]',
            '[class*="product"]',
            '[class*="subject"]',
            '[class*="title"]'
        ];

        for (let selector of subjectSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
                return element.textContent.trim();
            }
        }

        // 从页面文本中查找
        const pageText = document.body.textContent || '';
        const subjectMatch = pageText.match(/商品名称[：:]?\s*([^\n\r]+)/);
        if (subjectMatch) {
            return subjectMatch[1].trim();
        }

        return 'TMT护腕腱鞘炎防扭伤运动TFCC护腕';
    }

    interceptPaymentButtons() {
        // 拦截网银支付相关按钮
        const bankPaymentSelectors = [
            'a[href*="bankConfirm"]',
            'button:contains("跳转网银")',
            'a:contains("跳转网银")',
            'button:contains("网银支付")',
            'a:contains("网银支付")',
            '[onclick*="bank"]',
            '[class*="bank-pay"]',
            '[id*="bank-pay"]'
        ];

        bankPaymentSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    this.interceptElement(element);
                });
            } catch (error) {
                // 忽略选择器错误
            }
        });

        // 特殊处理：查找包含"跳转网银"文本的元素
        this.interceptTextElements();
    }

    interceptTextElements() {
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        let node;
        while (node = walker.nextNode()) {
            if (node.textContent.includes('跳转网银') || 
                node.textContent.includes('网银支付') ||
                node.textContent.includes('银行支付')) {
                
                const element = node.parentElement;
                if (element && (element.tagName === 'BUTTON' || 
                               element.tagName === 'A' || 
                               element.onclick || 
                               element.style.cursor === 'pointer')) {
                    this.interceptElement(element);
                }
            }
        }
    }

    interceptElement(element) {
        if (element.dataset.qrIntercepted) return;
        
        element.dataset.qrIntercepted = 'true';
        
        // 保存原始事件处理器
        const originalOnclick = element.onclick;
        const originalHref = element.href;

        // 添加我们的点击处理器
        element.addEventListener('click', (event) => {
            if (!this.isEnabled) return;
            
            event.preventDefault();
            event.stopPropagation();
            
            console.log('拦截网银支付跳转，显示二维码');
            this.showQRModal();
            
            return false;
        }, true);

        // 移除原始href以防止跳转
        if (element.href) {
            element.removeAttribute('href');
            element.style.cursor = 'pointer';
        }

        // 添加视觉提示
        this.addQRIndicator(element);
    }

    addQRIndicator(element) {
        // 添加二维码图标提示
        const indicator = document.createElement('span');
        indicator.innerHTML = ' 📱';
        indicator.title = '点击显示支付二维码';
        indicator.style.fontSize = '12px';
        element.appendChild(indicator);
    }

    showQRModal() {
        // 创建模态框
        const modal = this.createQRModal();
        document.body.appendChild(modal);
        
        // 生成二维码
        this.generateQRCode(modal);
        
        // 显示模态框
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }

    createQRModal() {
        const modal = document.createElement('div');
        modal.className = 'jd-qr-modal';
        modal.innerHTML = `
            <div class="jd-qr-modal-content">
                <div class="jd-qr-header">
                    <h3>🏦 银行支付二维码</h3>
                    <button class="jd-qr-close">&times;</button>
                </div>
                <div class="jd-qr-body">
                    <div class="jd-qr-info">
                        <p><strong>订单号:</strong> ${this.paymentData?.orderId || 'N/A'}</p>
                        <p><strong>金额:</strong> ¥${this.paymentData?.amount || '0.00'}</p>
                        <p><strong>商品:</strong> ${this.paymentData?.subject || 'N/A'}</p>
                    </div>
                    <div class="jd-qr-bank-selector">
                        <label>选择银行:</label>
                        <select id="bankSelect">
                            ${Object.entries(this.supportedBanks).map(([code, bank]) => 
                                `<option value="${code}" ${code === this.preferredBank ? 'selected' : ''}>
                                    ${bank.logo} ${bank.name}
                                </option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="jd-qr-code-container">
                        <div id="qrcode"></div>
                        <p class="jd-qr-tip">请使用手机银行APP扫描二维码完成支付</p>
                    </div>
                    <div class="jd-qr-actions">
                        <button id="refreshQR" class="jd-btn-secondary">🔄 刷新二维码</button>
                        <button id="copyPaymentUrl" class="jd-btn-secondary">📋 复制支付链接</button>
                    </div>
                </div>
            </div>
        `;

        // 绑定事件
        this.bindModalEvents(modal);
        
        return modal;
    }

    bindModalEvents(modal) {
        // 关闭按钮
        modal.querySelector('.jd-qr-close').onclick = () => {
            modal.classList.remove('show');
            setTimeout(() => modal.remove(), 300);
        };

        // 点击背景关闭
        modal.onclick = (e) => {
            if (e.target === modal) {
                modal.classList.remove('show');
                setTimeout(() => modal.remove(), 300);
            }
        };

        // 银行选择变化
        modal.querySelector('#bankSelect').onchange = (e) => {
            this.preferredBank = e.target.value;
            chrome.storage.sync.set({ preferredBank: this.preferredBank });
            this.generateQRCode(modal);
        };

        // 刷新二维码
        modal.querySelector('#refreshQR').onclick = () => {
            this.generateQRCode(modal);
        };

        // 复制支付链接
        modal.querySelector('#copyPaymentUrl').onclick = () => {
            const paymentUrl = this.generatePaymentUrl();
            navigator.clipboard.writeText(paymentUrl).then(() => {
                this.showToast('支付链接已复制到剪贴板');
            });
        };
    }

    generateQRCode(modal) {
        const qrContainer = modal.querySelector('#qrcode');
        qrContainer.innerHTML = '';
        
        const paymentUrl = this.generatePaymentUrl();
        
        // 使用qrcode.js生成二维码
        const qr = qrcode(0, 'M');
        qr.addData(paymentUrl);
        qr.make();
        
        qrContainer.innerHTML = qr.createImgTag(4);
        
        // 添加样式
        const img = qrContainer.querySelector('img');
        if (img) {
            img.style.maxWidth = '200px';
            img.style.height = 'auto';
            img.style.border = '1px solid #ddd';
            img.style.borderRadius = '8px';
        }
    }

    generatePaymentUrl() {
        const bankConfig = this.supportedBanks[this.preferredBank];
        const paymentData = this.paymentData || {};
        
        // 构建支付URL（这里是模拟的格式，实际需要根据各银行的API文档调整）
        const params = new URLSearchParams({
            bank: this.preferredBank,
            orderId: paymentData.orderId || `JD${Date.now()}`,
            amount: paymentData.amount || '35.61',
            subject: paymentData.subject || '商品购买',
            timestamp: Date.now(),
            source: 'jd_payment_qr_helper'
        });
        
        return `https://pay.example.com/bank/${this.preferredBank.toLowerCase()}?${params.toString()}`;
    }

    observePageChanges() {
        // 监听页面变化，重新拦截新添加的按钮
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.interceptPaymentButtons();
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    showToast(message) {
        const toast = document.createElement('div');
        toast.className = 'jd-qr-toast';
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => toast.classList.add('show'), 10);
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
}

// 初始化插件
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new JDPaymentQRHelper();
    });
} else {
    new JDPaymentQRHelper();
}
