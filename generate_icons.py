#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成浏览器插件图标
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, filename):
    """创建指定尺寸的图标"""
    # 创建图像
    img = Image.new('RGBA', (size, size), (0, 123, 255, 255))  # 蓝色背景
    draw = ImageDraw.Draw(img)
    
    # 绘制圆角矩形背景
    corner_radius = size // 8
    draw.rounded_rectangle(
        [(0, 0), (size-1, size-1)], 
        radius=corner_radius, 
        fill=(0, 123, 255, 255),
        outline=(255, 255, 255, 100),
        width=1
    )
    
    # 绘制二维码图案
    qr_size = size // 2
    qr_start = (size - qr_size) // 2
    
    # 绘制二维码方块
    block_size = qr_size // 6
    for i in range(6):
        for j in range(6):
            if (i + j) % 2 == 0:  # 棋盘图案
                x = qr_start + i * block_size
                y = qr_start + j * block_size
                draw.rectangle(
                    [(x, y), (x + block_size - 1, y + block_size - 1)],
                    fill=(255, 255, 255, 255)
                )
    
    # 绘制边框
    draw.rectangle([(1, 1), (size-2, size-2)], outline=(255, 255, 255, 150), width=1)
    
    # 保存图标
    img.save(filename, 'PNG')
    print(f"已生成图标: {filename}")

def main():
    """生成所有尺寸的图标"""
    # 确保icons目录存在
    os.makedirs('icons', exist_ok=True)
    
    # 生成不同尺寸的图标
    sizes = [16, 32, 48, 128]
    
    for size in sizes:
        filename = f'icons/icon{size}.png'
        create_icon(size, filename)
    
    print("所有图标生成完成！")

if __name__ == '__main__':
    main()
