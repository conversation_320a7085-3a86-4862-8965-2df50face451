#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实支付服务器
支持微信支付和支付宝的二维码支付
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import hashlib
import time
import uuid
import logging
import os
from datetime import datetime
import qrcode
import io
import base64

# 微信支付SDK
try:
    from wechatpayv3 import WeChatPay, WeChatPayType
except ImportError:
    print("请安装微信支付SDK: pip install wechatpayv3")
    WeChatPay = None

# 支付宝SDK
try:
    from alipay import AliPay
except ImportError:
    print("请安装支付宝SDK: pip install python-alipay-sdk")
    AliPay = None

app = Flask(__name__)
CORS(app)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 支付配置
PAYMENT_CONFIG = {
    'wechat': {
        'mchid': os.getenv('WECHAT_MCHID', '1900000109'),
        'appid': os.getenv('WECHAT_APPID', 'wxd678efh567hg6787'),
        'private_key': os.getenv('WECHAT_PRIVATE_KEY', ''),
        'cert_serial_no': os.getenv('WECHAT_CERT_SERIAL_NO', ''),
        'apiv3_key': os.getenv('WECHAT_APIV3_KEY', ''),
        'notify_url': os.getenv('WECHAT_NOTIFY_URL', 'https://your-domain.com/api/wechat/notify')
    },
    'alipay': {
        'appid': os.getenv('ALIPAY_APPID', '2021000000000000'),
        'app_private_key': os.getenv('ALIPAY_PRIVATE_KEY', ''),
        'alipay_public_key': os.getenv('ALIPAY_PUBLIC_KEY', ''),
        'sign_type': 'RSA2',
        'debug': os.getenv('ALIPAY_DEBUG', 'True').lower() == 'true',
        'notify_url': os.getenv('ALIPAY_NOTIFY_URL', 'https://your-domain.com/api/alipay/notify')
    }
}

# 初始化支付客户端
def init_wechat_pay():
    """初始化微信支付客户端"""
    if not WeChatPay:
        return None
    
    config = PAYMENT_CONFIG['wechat']
    try:
        return WeChatPay(
            wechatpay_type=WeChatPayType.NATIVE,
            mchid=config['mchid'],
            private_key=config['private_key'],
            cert_serial_no=config['cert_serial_no'],
            apiv3_key=config['apiv3_key'],
            appid=config['appid'],
            notify_url=config['notify_url']
        )
    except Exception as e:
        logger.error(f"初始化微信支付失败: {e}")
        return None

def init_alipay():
    """初始化支付宝客户端"""
    if not AliPay:
        return None
    
    config = PAYMENT_CONFIG['alipay']
    try:
        return AliPay(
            appid=config['appid'],
            app_notify_url=config['notify_url'],
            app_private_key_string=config['app_private_key'],
            alipay_public_key_string=config['alipay_public_key'],
            sign_type=config['sign_type'],
            debug=config['debug']
        )
    except Exception as e:
        logger.error(f"初始化支付宝失败: {e}")
        return None

# 初始化支付客户端
wechat_pay = init_wechat_pay()
alipay_client = init_alipay()

# 订单存储（生产环境应使用数据库）
orders = {}

def generate_order_id():
    """生成订单号"""
    timestamp = str(int(time.time()))
    random_str = str(uuid.uuid4()).replace('-', '')[:8]
    return f"JD{timestamp}{random_str}"

@app.route('/api/create-payment', methods=['POST'])
def create_payment():
    """创建支付订单"""
    try:
        data = request.get_json()
        
        # 验证必需参数
        required_fields = ['paymentMethod', 'amount', 'subject']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'缺少必需参数: {field}'
                }), 400
        
        payment_method = data['paymentMethod'].lower()
        amount = int(data['amount'])  # 金额（分）
        subject = data['subject']
        order_id = data.get('orderId', generate_order_id())
        
        # 创建订单记录
        order_data = {
            'orderId': order_id,
            'amount': amount,
            'subject': subject,
            'paymentMethod': payment_method,
            'status': 'pending',
            'createdAt': datetime.now().isoformat(),
            'notifyUrl': data.get('notifyUrl', ''),
            'returnUrl': data.get('returnUrl', '')
        }
        
        orders[order_id] = order_data
        
        # 根据支付方式创建支付订单
        if payment_method == 'wechat':
            result = create_wechat_payment(order_data)
        elif payment_method == 'alipay':
            result = create_alipay_payment(order_data)
        else:
            return jsonify({
                'success': False,
                'error': f'不支持的支付方式: {payment_method}'
            }), 400
        
        if result['success']:
            # 更新订单信息
            orders[order_id].update(result['data'])
            
            logger.info(f"创建支付订单成功 - 订单号: {order_id}, 支付方式: {payment_method}, 金额: {amount}")
            
            return jsonify({
                'success': True,
                'data': {
                    'orderId': order_id,
                    'paymentMethod': payment_method,
                    'amount': amount,
                    'subject': subject,
                    'qrCodeUrl': result['data'].get('qrCodeUrl'),
                    'paymentUrl': result['data'].get('paymentUrl'),
                    'createdAt': order_data['createdAt']
                }
            })
        else:
            return jsonify(result), 500
            
    except Exception as e:
        logger.error(f"创建支付订单失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def create_wechat_payment(order_data):
    """创建微信支付订单"""
    if not wechat_pay:
        return {
            'success': False,
            'error': '微信支付未配置或初始化失败'
        }
    
    try:
        # 调用微信支付API创建订单
        code, message = wechat_pay.pay(
            description=order_data['subject'],
            out_trade_no=order_data['orderId'],
            amount={'total': order_data['amount']},
            pay_type=WeChatPayType.NATIVE
        )
        
        if code == 200:
            # 解析返回的支付信息
            payment_info = json.loads(message)
            code_url = payment_info.get('code_url')
            
            if code_url:
                return {
                    'success': True,
                    'data': {
                        'paymentUrl': code_url,
                        'qrCodeUrl': code_url,
                        'prepayId': payment_info.get('prepay_id')
                    }
                }
            else:
                return {
                    'success': False,
                    'error': '微信支付返回数据异常'
                }
        else:
            return {
                'success': False,
                'error': f'微信支付创建订单失败: {message}'
            }
            
    except Exception as e:
        logger.error(f"创建微信支付订单失败: {e}")
        return {
            'success': False,
            'error': f'微信支付异常: {str(e)}'
        }

def create_alipay_payment(order_data):
    """创建支付宝支付订单"""
    if not alipay_client:
        return {
            'success': False,
            'error': '支付宝未配置或初始化失败'
        }
    
    try:
        # 构建支付宝订单参数
        order_string = alipay_client.api_alipay_trade_precreate(
            out_trade_no=order_data['orderId'],
            total_amount=order_data['amount'] / 100,  # 转换为元
            subject=order_data['subject'],
            notify_url=PAYMENT_CONFIG['alipay']['notify_url']
        )
        
        # 解析返回结果
        if order_string:
            # 这里需要根据支付宝SDK的实际返回格式进行调整
            return {
                'success': True,
                'data': {
                    'paymentUrl': order_string,
                    'qrCodeUrl': order_string
                }
            }
        else:
            return {
                'success': False,
                'error': '支付宝创建订单失败'
            }
            
    except Exception as e:
        logger.error(f"创建支付宝订单失败: {e}")
        return {
            'success': False,
            'error': f'支付宝异常: {str(e)}'
        }

@app.route('/api/payment-status/<order_id>')
def get_payment_status(order_id):
    """查询支付状态"""
    try:
        if order_id not in orders:
            return jsonify({
                'success': False,
                'error': '订单不存在'
            }), 404
        
        order = orders[order_id]
        
        # 这里可以调用支付平台API查询真实状态
        # 为演示目的，我们返回订单当前状态
        
        return jsonify({
            'success': True,
            'data': {
                'orderId': order_id,
                'status': order['status'],
                'amount': order['amount'],
                'paymentMethod': order['paymentMethod'],
                'updatedAt': datetime.now().isoformat()
            }
        })
        
    except Exception as e:
        logger.error(f"查询支付状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/wechat/notify', methods=['POST'])
def wechat_notify():
    """微信支付回调"""
    try:
        if not wechat_pay:
            return jsonify({'code': 'FAIL', 'message': '微信支付未配置'}), 500
        
        # 验证回调签名并解密
        result = wechat_pay.callback(request.headers, request.data)
        
        if result and result.get('event_type') == 'TRANSACTION.SUCCESS':
            resource = result.get('resource')
            out_trade_no = resource.get('out_trade_no')
            transaction_id = resource.get('transaction_id')
            trade_state = resource.get('trade_state')
            
            # 更新订单状态
            if out_trade_no in orders:
                orders[out_trade_no]['status'] = 'paid' if trade_state == 'SUCCESS' else 'failed'
                orders[out_trade_no]['transactionId'] = transaction_id
                orders[out_trade_no]['paidAt'] = datetime.now().isoformat()
                
                logger.info(f"微信支付回调成功 - 订单: {out_trade_no}, 状态: {trade_state}")
            
            return jsonify({'code': 'SUCCESS', 'message': '成功'})
        else:
            logger.error("微信支付回调验证失败")
            return jsonify({'code': 'FAIL', 'message': '验证失败'}), 400
            
    except Exception as e:
        logger.error(f"微信支付回调处理失败: {e}")
        return jsonify({'code': 'FAIL', 'message': '处理失败'}), 500

@app.route('/api/alipay/notify', methods=['POST'])
def alipay_notify():
    """支付宝支付回调"""
    try:
        if not alipay_client:
            return 'fail'
        
        # 获取支付宝POST过来反馈信息
        data = request.form.to_dict()
        signature = data.pop('sign')
        
        # 验证签名
        success = alipay_client.verify(data, signature)
        
        if success:
            out_trade_no = data.get('out_trade_no')
            trade_status = data.get('trade_status')
            
            # 更新订单状态
            if out_trade_no in orders:
                if trade_status in ['TRADE_SUCCESS', 'TRADE_FINISHED']:
                    orders[out_trade_no]['status'] = 'paid'
                else:
                    orders[out_trade_no]['status'] = 'failed'
                
                orders[out_trade_no]['transactionId'] = data.get('trade_no')
                orders[out_trade_no]['paidAt'] = datetime.now().isoformat()
                
                logger.info(f"支付宝回调成功 - 订单: {out_trade_no}, 状态: {trade_status}")
            
            return 'success'
        else:
            logger.error("支付宝回调验证失败")
            return 'fail'
            
    except Exception as e:
        logger.error(f"支付宝回调处理失败: {e}")
        return 'fail'

@app.route('/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'wechat_available': wechat_pay is not None,
        'alipay_available': alipay_client is not None
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
