<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>京东支付二维码助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 350px;
            min-height: 400px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            text-align: center;
            color: white;
        }

        .header h1 {
            font-size: 18px;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 12px;
            opacity: 0.9;
        }

        .content {
            background: white;
            padding: 20px;
            border-radius: 12px 12px 0 0;
            margin-top: -10px;
        }

        .status-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
        }

        .status-card.disabled {
            border-left-color: #dc3545;
        }

        .status-card h3 {
            font-size: 14px;
            margin-bottom: 8px;
            color: #333;
        }

        .status-card p {
            font-size: 12px;
            color: #666;
            margin: 4px 0;
        }

        .toggle-section {
            margin-bottom: 20px;
        }

        .toggle-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }

        .toggle-item:last-child {
            border-bottom: none;
        }

        .toggle-label {
            font-size: 14px;
            color: #333;
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .toggle-switch.active {
            background: #007bff;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }

        .toggle-switch.active::after {
            transform: translateX(20px);
        }

        .bank-selector {
            margin-bottom: 20px;
        }

        .bank-selector label {
            display: block;
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .bank-selector select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 13px;
            background: white;
        }

        .bank-selector select:focus {
            outline: none;
            border-color: #007bff;
        }

        .stats-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .stats-section h3 {
            font-size: 14px;
            margin-bottom: 12px;
            color: #333;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .stat-item {
            text-align: center;
            padding: 8px;
            background: white;
            border-radius: 6px;
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            font-size: 11px;
            color: #666;
            margin-top: 4px;
        }

        .actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .footer {
            text-align: center;
            padding: 16px;
            font-size: 11px;
            color: #666;
            background: #f8f9fa;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏦 京东支付二维码助手</h1>
        <p>拦截网银跳转，直接显示支付二维码</p>
    </div>

    <div class="content">
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>加载中...</p>
        </div>

        <div id="mainContent">
            <div class="status-card" id="statusCard">
                <h3 id="statusTitle">🟢 插件已启用</h3>
                <p id="statusDesc">正在监控京东支付页面</p>
                <p id="lastUsed">上次使用: 从未使用</p>
            </div>

            <div class="toggle-section">
                <div class="toggle-item">
                    <span class="toggle-label">启用插件</span>
                    <div class="toggle-switch active" id="enableToggle"></div>
                </div>
                <div class="toggle-item">
                    <span class="toggle-label">自动检测</span>
                    <div class="toggle-switch active" id="autoDetectToggle"></div>
                </div>
                <div class="toggle-item">
                    <span class="toggle-label">显示通知</span>
                    <div class="toggle-switch active" id="notificationToggle"></div>
                </div>
            </div>

            <div class="bank-selector">
                <label for="bankSelect">默认银行:</label>
                <select id="bankSelect">
                    <option value="ICBC">🏦 中国工商银行</option>
                    <option value="CCB">🏛️ 中国建设银行</option>
                    <option value="ABC">🌾 中国农业银行</option>
                    <option value="BOC">🏪 中国银行</option>
                    <option value="COMM">🚇 交通银行</option>
                    <option value="CMB">💳 招商银行</option>
                    <option value="PSBC">📮 中国邮政储蓄银行</option>
                    <option value="CITIC">🏢 中信银行</option>
                </select>
            </div>

            <div class="stats-section">
                <h3>📊 使用统计</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="interceptCount">0</div>
                        <div class="stat-label">拦截次数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="todayCount">0</div>
                        <div class="stat-label">今日使用</div>
                    </div>
                </div>
            </div>

            <div class="actions">
                <button class="btn btn-secondary" id="resetBtn">🔄 重置</button>
                <button class="btn btn-primary" id="testBtn">🧪 测试</button>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>版本 1.0.0 | 让支付更简单</p>
    </div>

    <script src="popup.js"></script>
</body>
</html>
