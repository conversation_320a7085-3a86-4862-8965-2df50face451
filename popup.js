/**
 * 京东支付二维码助手 - 弹出页面脚本
 */

class PopupManager {
    constructor() {
        this.settings = {};
        this.stats = {};
        this.init();
    }

    async init() {
        console.log('弹出页面初始化');
        
        // 显示加载状态
        this.showLoading(true);
        
        try {
            // 加载设置和统计信息
            await this.loadSettings();
            await this.loadStatistics();
            
            // 初始化UI
            this.initializeUI();
            this.bindEvents();
            
            // 隐藏加载状态
            this.showLoading(false);
        } catch (error) {
            console.error('初始化失败:', error);
            this.showError('初始化失败，请重试');
        }
    }

    showLoading(show) {
        const loading = document.getElementById('loading');
        const mainContent = document.getElementById('mainContent');
        
        if (show) {
            loading.classList.add('show');
            mainContent.style.display = 'none';
        } else {
            loading.classList.remove('show');
            mainContent.style.display = 'block';
        }
    }

    async loadSettings() {
        try {
            const response = await this.sendMessage({ type: 'GET_SETTINGS' });
            if (response.success) {
                this.settings = response.data;
                console.log('设置已加载:', this.settings);
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('加载设置失败:', error);
            // 使用默认设置
            this.settings = {
                enabled: true,
                preferredBank: 'ICBC',
                autoDetect: true,
                showNotifications: true
            };
        }
    }

    async loadStatistics() {
        try {
            const response = await this.sendMessage({ type: 'GET_STATISTICS' });
            if (response.success) {
                this.stats = response.data;
                console.log('统计信息已加载:', this.stats);
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('加载统计信息失败:', error);
            this.stats = {
                interceptCount: 0,
                lastIntercepted: null,
                lastPaymentData: null
            };
        }
    }

    initializeUI() {
        // 更新状态卡片
        this.updateStatusCard();
        
        // 设置开关状态
        this.setToggleState('enableToggle', this.settings.enabled);
        this.setToggleState('autoDetectToggle', this.settings.autoDetect);
        this.setToggleState('notificationToggle', this.settings.showNotifications);
        
        // 设置银行选择
        const bankSelect = document.getElementById('bankSelect');
        bankSelect.value = this.settings.preferredBank || 'ICBC';
        
        // 更新统计信息
        this.updateStatistics();
    }

    updateStatusCard() {
        const statusCard = document.getElementById('statusCard');
        const statusTitle = document.getElementById('statusTitle');
        const statusDesc = document.getElementById('statusDesc');
        const lastUsed = document.getElementById('lastUsed');

        if (this.settings.enabled) {
            statusCard.classList.remove('disabled');
            statusTitle.textContent = '🟢 插件已启用';
            statusDesc.textContent = '正在监控京东支付页面';
        } else {
            statusCard.classList.add('disabled');
            statusTitle.textContent = '🔴 插件已禁用';
            statusDesc.textContent = '点击开关启用插件';
        }

        // 更新最后使用时间
        if (this.stats.lastIntercepted) {
            const lastTime = new Date(this.stats.lastIntercepted);
            lastUsed.textContent = `上次使用: ${this.formatTime(lastTime)}`;
        } else {
            lastUsed.textContent = '上次使用: 从未使用';
        }
    }

    updateStatistics() {
        const interceptCount = document.getElementById('interceptCount');
        const todayCount = document.getElementById('todayCount');

        interceptCount.textContent = this.stats.interceptCount || 0;
        
        // 计算今日使用次数（简化版本）
        const today = new Date().toDateString();
        const lastUsedDate = this.stats.lastIntercepted ? 
            new Date(this.stats.lastIntercepted).toDateString() : null;
        
        todayCount.textContent = (lastUsedDate === today) ? '1+' : '0';
    }

    setToggleState(toggleId, isActive) {
        const toggle = document.getElementById(toggleId);
        if (isActive) {
            toggle.classList.add('active');
        } else {
            toggle.classList.remove('active');
        }
    }

    bindEvents() {
        // 开关事件
        document.getElementById('enableToggle').addEventListener('click', () => {
            this.toggleSetting('enabled');
        });

        document.getElementById('autoDetectToggle').addEventListener('click', () => {
            this.toggleSetting('autoDetect');
        });

        document.getElementById('notificationToggle').addEventListener('click', () => {
            this.toggleSetting('showNotifications');
        });

        // 银行选择事件
        document.getElementById('bankSelect').addEventListener('change', (e) => {
            this.updateSetting('preferredBank', e.target.value);
        });

        // 按钮事件
        document.getElementById('resetBtn').addEventListener('click', () => {
            this.resetSettings();
        });

        document.getElementById('testBtn').addEventListener('click', () => {
            this.testFunction();
        });
    }

    async toggleSetting(settingName) {
        const currentValue = this.settings[settingName];
        const newValue = !currentValue;
        
        await this.updateSetting(settingName, newValue);
        
        // 更新UI
        const toggleId = settingName === 'enabled' ? 'enableToggle' :
                        settingName === 'autoDetect' ? 'autoDetectToggle' :
                        'notificationToggle';
        
        this.setToggleState(toggleId, newValue);
        
        // 如果是启用/禁用插件，更新状态卡片
        if (settingName === 'enabled') {
            this.updateStatusCard();
        }
    }

    async updateSetting(settingName, value) {
        try {
            this.settings[settingName] = value;
            
            const response = await this.sendMessage({
                type: 'UPDATE_SETTINGS',
                data: { [settingName]: value }
            });
            
            if (!response.success) {
                throw new Error(response.error);
            }
            
            console.log(`设置已更新: ${settingName} = ${value}`);
        } catch (error) {
            console.error('更新设置失败:', error);
            this.showError('设置更新失败');
        }
    }

    async resetSettings() {
        if (!confirm('确定要重置所有设置吗？')) {
            return;
        }

        try {
            const defaultSettings = {
                enabled: true,
                preferredBank: 'ICBC',
                autoDetect: true,
                showNotifications: true
            };

            const response = await this.sendMessage({
                type: 'UPDATE_SETTINGS',
                data: defaultSettings
            });

            if (response.success) {
                this.settings = defaultSettings;
                this.initializeUI();
                this.showSuccess('设置已重置');
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('重置设置失败:', error);
            this.showError('重置失败');
        }
    }

    async testFunction() {
        try {
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab.url.includes('jd.com')) {
                this.showError('请在京东支付页面使用测试功能');
                return;
            }

            // 向内容脚本发送测试消息
            await chrome.tabs.sendMessage(tab.id, {
                type: 'TEST_QR_MODAL',
                data: {
                    orderId: 'TEST' + Date.now(),
                    amount: '0.01',
                    subject: '测试商品'
                }
            });

            this.showSuccess('测试二维码已显示');
        } catch (error) {
            console.error('测试失败:', error);
            this.showError('测试失败，请确保在京东支付页面');
        }
    }

    sendMessage(message) {
        return new Promise((resolve) => {
            chrome.runtime.sendMessage(message, (response) => {
                resolve(response || { success: false, error: '无响应' });
            });
        });
    }

    formatTime(date) {
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return `${Math.floor(diff / 60000)}分钟前`;
        } else if (diff < 86400000) { // 1天内
            return `${Math.floor(diff / 3600000)}小时前`;
        } else {
            return date.toLocaleDateString();
        }
    }

    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showError(message) {
        this.showToast(message, 'error');
    }

    showToast(message, type = 'info') {
        // 创建简单的提示
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            color: white;
            z-index: 1000;
            background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
        `;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new PopupManager();
});
