/* 京东支付二维码助手样式 */

.jd-qr-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999999;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.jd-qr-modal.show {
    opacity: 1;
    visibility: visible;
}

.jd-qr-modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 450px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.jd-qr-modal.show .jd-qr-modal-content {
    transform: scale(1);
}

.jd-qr-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #eee;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.jd-qr-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.jd-qr-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.jd-qr-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.jd-qr-body {
    padding: 24px;
}

.jd-qr-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.jd-qr-info p {
    margin: 8px 0;
    font-size: 14px;
    color: #333;
}

.jd-qr-info strong {
    color: #007bff;
    font-weight: 600;
}

.jd-qr-bank-selector {
    margin-bottom: 20px;
}

.jd-qr-bank-selector label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.jd-qr-bank-selector select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    background-color: white;
    transition: border-color 0.2s;
}

.jd-qr-bank-selector select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.jd-qr-code-container {
    text-align: center;
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
}

.jd-qr-code-container #qrcode {
    margin-bottom: 16px;
}

.jd-qr-code-container img {
    max-width: 200px !important;
    height: auto !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.jd-qr-tip {
    color: #6c757d;
    font-size: 13px;
    margin: 0;
    line-height: 1.4;
}

.jd-qr-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.jd-btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 6px;
}

.jd-btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.jd-btn-secondary:active {
    transform: translateY(0);
}

/* Toast 通知样式 */
.jd-qr-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    z-index: 1000000;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.jd-qr-toast.show {
    opacity: 1;
    transform: translateX(0);
}

/* 拦截按钮的视觉提示 */
[data-qr-intercepted="true"] {
    position: relative;
}

[data-qr-intercepted="true"]:after {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid #007bff;
    border-radius: 4px;
    opacity: 0.3;
    pointer-events: none;
    animation: qr-pulse 2s infinite;
}

@keyframes qr-pulse {
    0% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.02);
    }
    100% {
        opacity: 0.3;
        transform: scale(1);
    }
}

/* 响应式设计 */
@media (max-width: 480px) {
    .jd-qr-modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .jd-qr-header {
        padding: 16px 20px 12px;
    }
    
    .jd-qr-header h3 {
        font-size: 16px;
    }
    
    .jd-qr-body {
        padding: 20px;
    }
    
    .jd-qr-actions {
        flex-direction: column;
    }
    
    .jd-btn-secondary {
        width: 100%;
        justify-content: center;
    }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    .jd-qr-modal-content {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .jd-qr-info {
        background: #4a5568;
        border-left-color: #63b3ed;
    }
    
    .jd-qr-info strong {
        color: #63b3ed;
    }
    
    .jd-qr-bank-selector select {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .jd-qr-code-container {
        background: #4a5568;
        border-color: #718096;
    }
    
    .jd-qr-tip {
        color: #a0aec0;
    }
}
