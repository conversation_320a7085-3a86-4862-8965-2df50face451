#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证浏览器插件文件完整性
"""

import os
import json

def check_file_exists(filepath, description):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        size = os.path.getsize(filepath)
        print(f"✅ {description}: {filepath} ({size} bytes)")
        return True
    else:
        print(f"❌ {description}: {filepath} - 文件不存在")
        return False

def verify_manifest():
    """验证manifest.json文件"""
    try:
        with open('manifest.json', 'r', encoding='utf-8') as f:
            manifest = json.load(f)
        
        print("✅ manifest.json 语法正确")
        
        # 检查必需字段
        required_fields = ['manifest_version', 'name', 'version', 'permissions']
        for field in required_fields:
            if field in manifest:
                print(f"✅ 必需字段 '{field}': {manifest[field]}")
            else:
                print(f"❌ 缺少必需字段: {field}")
        
        return True
    except json.JSONDecodeError as e:
        print(f"❌ manifest.json 语法错误: {e}")
        return False
    except FileNotFoundError:
        print("❌ manifest.json 文件不存在")
        return False

def main():
    """主验证函数"""
    print("🔍 验证京东支付二维码助手插件文件...")
    print("=" * 50)
    
    all_good = True
    
    # 验证核心文件
    core_files = [
        ('manifest.json', '插件清单文件'),
        ('content.js', '内容脚本'),
        ('background.js', '后台脚本'),
        ('popup.html', '弹出页面'),
        ('popup.js', '弹出页面脚本'),
        ('styles.css', '样式文件'),
        ('libs/qrcode.min.js', 'QR码生成库')
    ]
    
    print("\n📁 核心文件检查:")
    for filepath, description in core_files:
        if not check_file_exists(filepath, description):
            all_good = False
    
    # 验证图标文件
    print("\n🖼️ 图标文件检查:")
    icon_sizes = [16, 32, 48, 128]
    for size in icon_sizes:
        filepath = f'icons/icon{size}.png'
        if not check_file_exists(filepath, f'{size}x{size} 图标'):
            all_good = False
    
    # 验证manifest.json
    print("\n📋 Manifest文件验证:")
    if not verify_manifest():
        all_good = False
    
    # 验证目录结构
    print("\n📂 目录结构检查:")
    required_dirs = ['icons', 'libs']
    for dirname in required_dirs:
        if os.path.isdir(dirname):
            print(f"✅ 目录存在: {dirname}/")
        else:
            print(f"❌ 目录不存在: {dirname}/")
            all_good = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_good:
        print("🎉 所有文件验证通过！插件可以安装。")
        print("\n📋 安装步骤:")
        print("1. 打开 Chrome 浏览器")
        print("2. 访问 chrome://extensions/")
        print("3. 启用 '开发者模式'")
        print("4. 点击 '加载已解压的扩展程序'")
        print("5. 选择当前文件夹")
    else:
        print("❌ 发现问题，请修复后重试。")
        print("\n🔧 可能的解决方案:")
        print("- 运行 'python generate_icons.py' 生成图标")
        print("- 检查文件路径和名称是否正确")
        print("- 确保所有文件都在正确位置")

if __name__ == '__main__':
    main()
